import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/derivative/derivatives_order_book/derivatives_order_book_cubit.dart';
import 'package:vp_trading/cubit/derivative/derivatives_order_book_filter/derivatives_order_book_filter_cubit.dart';
import 'package:vp_trading/cubit/derivatives_condition_order/derivatives_condition_order_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/conditional_order_page/conditional_order_page.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/regular_order_book/regular_order_page.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/widget/filter_derivatives_order_book.dart';

class DerivativesOrderBookScreen extends StatefulWidget {
  const DerivativesOrderBookScreen({super.key});

  @override
  State<DerivativesOrderBookScreen> createState() =>
      _DerivativesOrderBookScreenState();
}

class _DerivativesOrderBookScreenState extends State<DerivativesOrderBookScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late DerivativesOrderBookCubit _cubit;
  late DerivativesOrderBookFilterCubit _filterCubit;
  late DerivativesConditionOrderCubit _conditionOrderCubit;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _cubit = DerivativesOrderBookCubit()..init();
    _filterCubit = DerivativesOrderBookFilterCubit()..init();
    _conditionOrderCubit = DerivativesConditionOrderCubit()..init();

    // Listen to tab changes to update filter cubit
    _tabController.addListener(_onTabChanged);
  }

  @override
  void dispose() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    _cubit.close();
    _filterCubit.close();
    super.dispose();
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging) {
      _filterCubit.changeTab(_tabController.index);
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: _cubit),
        BlocProvider.value(value: _filterCubit),
        BlocProvider.value(value: _conditionOrderCubit),
      ],
      child: VPScaffold(
        appBar: VPAppBar.layer(
          backgroundColor: vpColor.backgroundElevation0,
          title: "Sổ lệnh",
          subTitle: 'Phái sinh',
          revertSubTitle: true,
          actions: [_buildActionBar(context)],
        ),
        body: _buildTabBarView(),
      ),
    );
  }

  Widget _buildTabBarView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        ColoredBox(
          color: vpColor.backgroundElevation0,
          child: VPTabBar(
            controller: _tabController,
            tabs: [
              Tab(text: VPTradingLocalize.current.trading_normal),
              Tab(text: VPTradingLocalize.current.trading_order_type_condition),
            ],
          ),
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            physics: const RangeMaintainingScrollPhysics(),
            children: const [RegularOrderPage(), ConditionalOrderPage()],
          ),
        ),
      ],
    );
  }

  _buildActionBar(BuildContext context) {
    return BlocConsumer<
      DerivativesOrderBookFilterCubit,
      DerivativesOrderBookFilterState
    >(
      listener: (context, filterState) {
        // Auto-trigger API call when filter changes
        final filterRequest = filterState.currentOrderRequest;
        if (filterRequest != null && filterState.currentTabIndex == 0) {
          _cubit.applyFilter(filterRequest);
        }
        if (filterRequest != null && filterState.currentTabIndex == 1) {
          _conditionOrderCubit.applyFilter(filterRequest);
        }
      },
      builder: (context, filterState) {
        final isFilterActive = filterState.currentFilter?.isDefault == false;
        return Row(
          children: [
            GestureDetector(
              onTap: _showFilterBottomSheet,
              child: DesignAssets.icons.icFilter.svg(
                colorFilter: ColorFilter.mode(
                  isFilterActive ? vpColor.textBrand : vpColor.iconPrimary,
                  BlendMode.srcIn,
                ),
                height: 20,
              ),
            ),
            const SizedBox(width: 16),
          ],
        );
      },
    );
  }

  void _showFilterBottomSheet() {
    final currentFilter = _filterCubit.state.currentFilter;
    final currentTabIndex = _filterCubit.state.currentTabIndex;
    openFilterDerivativesOrderBottomSheet(
      context,
      initialFilter: currentFilter,
      currentTabIndex: currentTabIndex,
      onApply: (filterParam) {
        _filterCubit.updateCurrentFilter(filterParam);
      },
    );
  }
}
